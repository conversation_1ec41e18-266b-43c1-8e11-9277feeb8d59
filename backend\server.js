const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const connectDB = require('./config/database');
const { initializeFirebase } = require('./config/firebase');
const authMiddleware = require('./middlewares/auth');
const socketAuth = require('./middlewares/socketAuth');

// Import routes
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const chatRoutes = require('./routes/chats');
const messageRoutes = require('./routes/messages');

// Import socket handlers
const socketHandlers = require('./sockets/socketHandlers');

const app = express();
const server = http.createServer(app);

// Initialize Firebase Admin
initializeFirebase();

// Connect to MongoDB
connectDB();

// CORS configuration
const corsOptions = {
  origin: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:5173'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization']
};

// Socket.io configuration
const io = socketIo(server, {
  cors: corsOptions,
  transports: ['websocket', 'polling']
});

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});

// Middleware
app.use(helmet());
app.use(limiter);
app.use(cors(corsOptions));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check route
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Test route for debugging (without auth)
app.post('/api/test/message/:chatId', async (req, res) => {
  try {
    const { chatId } = req.params;
    const { text, userId } = req.body;

    console.log(`🧪 TEST: Attempting to send message to chat ${chatId} from user ${userId}`);
    console.log(`🧪 TEST: Message text: "${text}"`);

    if (!text || !userId) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Message text and userId are required'
      });
    }

    const Chat = require('./models/Chat');
    const Message = require('./models/Message');

    // Find chat
    const chat = await Chat.findById(chatId);
    if (!chat) {
      console.log(`🧪 TEST: Chat not found: ${chatId}`);
      return res.status(404).json({
        error: 'Not Found',
        message: 'Chat not found'
      });
    }

    console.log(`🧪 TEST: Chat found, members:`, chat.members.map(m => m.user));

    // Create message
    const message = new Message({
      text: text.trim(),
      type: 'text',
      chatId,
      senderId: userId
    });

    await message.save();
    await message.populate('senderId', 'name avatar');

    console.log(`🧪 TEST: Message saved with ID: ${message._id}`);

    // Update chat
    chat.lastMessage = message._id;
    await chat.updateLastActivity();

    console.log(`🧪 TEST: Chat updated`);

    // Broadcast via socket if available
    if (io) {
      io.to(`chat:${chatId}`).emit('message:new', {
        message: message.toObject()
      });
      console.log(`🧪 TEST: Message broadcasted via socket`);
    }

    res.status(201).json({
      success: true,
      message: message.toObject()
    });

  } catch (error) {
    console.error('🧪 TEST: Error:', error.message);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to send test message'
    });
  }
});

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', authMiddleware, userRoutes);
app.use('/api/chats', authMiddleware, chatRoutes);
app.use('/api/messages', authMiddleware, messageRoutes);

// Socket.io authentication middleware
io.use(socketAuth);

// Socket.io connection handling
io.on('connection', (socket) => {
  console.log(`User connected: ${socket.userId}`);
  socketHandlers(io, socket);
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ 
    error: 'Something went wrong!',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

const PORT = process.env.PORT || 5000;

server.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📱 Socket.io server ready`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});

module.exports = { app, io };
