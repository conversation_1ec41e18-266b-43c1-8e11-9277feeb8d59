<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Message Sending</title>
    <script src="https://cdn.socket.io/4.7.4/socket.io.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; }
        input, button { padding: 10px; margin: 5px; }
        input[type="text"] { width: 300px; }
        .status { padding: 5px; margin: 5px 0; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Message Sending Test</h1>
        
        <div class="status info" id="connectionStatus">
            🔌 Connecting to server...
        </div>
        
        <div>
            <h3>Send Test Message:</h3>
            <input type="text" id="chatId" placeholder="Chat ID" value="6845417f0da129a2ccf4a38c">
            <br>
            <input type="text" id="messageText" placeholder="Type your message..." value="Test message from HTML">
            <br>
            <button onclick="sendMessage()">📤 Send Message</button>
            <button onclick="clearLogs()">🗑️ Clear Logs</button>
        </div>
        
        <div>
            <h3>📋 Logs:</h3>
            <div id="logs"></div>
        </div>
    </div>

    <script>
        let socket;
        const logs = document.getElementById('logs');
        const status = document.getElementById('connectionStatus');
        
        function addLog(message, type = 'info') {
            const logDiv = document.createElement('div');
            logDiv.className = `log ${type}`;
            logDiv.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            logs.appendChild(logDiv);
            logs.scrollTop = logs.scrollHeight;
            console.log(message);
        }
        
        function clearLogs() {
            logs.innerHTML = '';
        }
        
        function updateStatus(message, type = 'info') {
            status.className = `status ${type}`;
            status.innerHTML = message;
        }
        
        // Initialize socket connection
        function initSocket() {
            addLog('🔌 Attempting to connect to backend...', 'info');
            
            // You'll need to replace this with a valid Firebase token
            const testToken = 'test-token'; // This won't work without real auth
            
            socket = io('http://localhost:5000', {
                auth: { token: testToken },
                transports: ['websocket', 'polling']
            });
            
            socket.on('connect', () => {
                addLog('✅ Connected to server!', 'success');
                updateStatus('🟢 Connected', 'success');
            });
            
            socket.on('disconnect', (reason) => {
                addLog(`❌ Disconnected: ${reason}`, 'error');
                updateStatus('🔴 Disconnected', 'error');
            });
            
            socket.on('connect_error', (error) => {
                addLog(`❌ Connection error: ${error.message}`, 'error');
                updateStatus('🔴 Connection Failed', 'error');
            });
            
            socket.on('error', (error) => {
                addLog(`❌ Socket error: ${error.message}`, 'error');
            });
            
            socket.on('message:new', (data) => {
                addLog(`📨 New message received: ${JSON.stringify(data)}`, 'success');
            });
            
            socket.on('message:sent', (data) => {
                addLog(`✅ Message sent confirmation: ${JSON.stringify(data)}`, 'success');
            });
            
            socket.on('message:error', (error) => {
                addLog(`❌ Message error: ${JSON.stringify(error)}`, 'error');
            });
        }
        
        function sendMessage() {
            const chatId = document.getElementById('chatId').value;
            const text = document.getElementById('messageText').value;
            
            if (!chatId || !text) {
                addLog('❌ Please enter both Chat ID and message text', 'error');
                return;
            }
            
            if (!socket || !socket.connected) {
                addLog('❌ Socket not connected', 'error');
                return;
            }
            
            const tempId = `temp_${Date.now()}`;
            
            addLog(`📤 Sending message to chat ${chatId}: "${text}"`, 'info');
            
            socket.emit('message:send', {
                chatId: chatId,
                text: text,
                tempId: tempId
            });
            
            addLog(`📡 Message emitted with tempId: ${tempId}`, 'info');
        }
        
        // Test API endpoint directly
        async function testAPIEndpoint() {
            const chatId = document.getElementById('chatId').value;
            const text = document.getElementById('messageText').value;
            
            try {
                addLog('🌐 Testing API endpoint directly...', 'info');
                
                const response = await fetch(`http://localhost:5000/api/messages/${chatId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer test-token' // This won't work without real auth
                    },
                    body: JSON.stringify({
                        text: text,
                        type: 'text'
                    })
                });
                
                const result = await response.json();
                addLog(`🌐 API Response: ${JSON.stringify(result)}`, response.ok ? 'success' : 'error');
                
            } catch (error) {
                addLog(`❌ API Error: ${error.message}`, 'error');
            }
        }
        
        // Initialize when page loads
        window.onload = function() {
            addLog('🚀 Page loaded, initializing socket...', 'info');
            initSocket();
        };
    </script>
</body>
</html>
