// Simple test to send message via API
const axios = require('axios');

async function testMessageAPI() {
  try {
    console.log('🧪 Testing message API endpoint...');
    
    const chatId = '6845417f0da129a2ccf4a38c'; // Existing chat ID
    const userId = '6845416a0da129a2ccf4a366'; // Existing user ID
    const messageText = `Test message from API at ${new Date().toLocaleTimeString()}`;
    
    console.log(`📤 Sending message to chat: ${chatId}`);
    console.log(`👤 From user: ${userId}`);
    console.log(`💬 Message: "${messageText}"`);
    
    const response = await axios.post(`http://localhost:5000/api/test/message/${chatId}`, {
      text: messageText,
      userId: userId
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ API Response:', response.data);
    console.log('📊 Status:', response.status);
    
    if (response.data.success) {
      console.log('🎉 Message sent successfully!');
      console.log('📝 Message ID:', response.data.message._id);
      console.log('👤 Sender:', response.data.message.senderId.name);
    }
    
  } catch (error) {
    console.error('❌ API Test failed:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
  }
}

testMessageAPI();
